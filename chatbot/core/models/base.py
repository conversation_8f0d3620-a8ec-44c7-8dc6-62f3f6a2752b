from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import torch


class BaseChatModel(ABC):
    """Abstract base class for chat models"""

    def __init__(self, model_config: 'ModelConfig'):
        self.config = model_config
        self.model = None
        self.tokenizer = None
        self.device = None

    @abstractmethod
    def load_model(self) -> None:
        """Load the model and tokenizer"""
        pass

    @abstractmethod
    def generate_response(self, prompt: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """Generate a response to the given prompt"""
        pass

    @abstractmethod
    def cleanup(self) -> None:
        """Clean up model resources"""
        pass

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            "name": self.config.name,
            "model_path": self.config.model_path,
            "device": str(self.device) if self.device else "None",
            "parameters": self.get_parameter_count()
        }

    def get_parameter_count(self) -> int:
        """Get number of parameters in the model"""
        if self.model is None:
            return 0
        return sum(p.numel() for p in self.model.parameters())

    def check_gpu_memory(self) -> Dict[str, float]:
        """Check GPU memory usage"""
        if not torch.cuda.is_available():
            return {"error": "CUDA not available"}

        return {
            "allocated": torch.cuda.memory_allocated() / 1024 ** 3,
            "reserved": torch.cuda.memory_reserved() / 1024 ** 3,
            "max_allocated": torch.cuda.max_memory_allocated() / 1024 ** 3
        }