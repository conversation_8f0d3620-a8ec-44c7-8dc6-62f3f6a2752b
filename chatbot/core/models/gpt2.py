import torch
import re
from transformers import GPT<PERSON>LM<PERSON>eadModel, GPT2Tokenizer, BitsAndBytesConfig
from .base import BaseChatModel
from typing import List, Dict, Optional


class GPT2ChatModel(BaseChatModel):
    """GPT-2 Large model implementation"""

    def load_model(self) -> None:
        """Load GPT-2 model with optimizations"""
        print(f"Loading {self.config.name} model...")

        # Load tokenizer
        self.tokenizer = GPT2Tokenizer.from_pretrained(self.config.model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # Configure quantization if enabled
        quantization_config = None
        if self.config.use_quantization and torch.cuda.is_available():
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_compute_dtype=torch.float16
            )

        # Load model
        self.model = GPT2LMHeadModel.from_pretrained(
            self.config.model_path,
            quantization_config=quantization_config,
            device_map=self.config.device_map if torch.cuda.is_available() else None,
            torch_dtype=getattr(torch, self.config.torch_dtype) if torch.cuda.is_available() else torch.float32,
            low_cpu_mem_usage=True
        )

        self.model.eval()
        self.device = next(self.model.parameters()).device
        print(f"Model loaded on {self.device}")

    def generate_response(self, prompt: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """Generate response using GPT-2"""
        # Build context from conversation history
        context = self._build_context(prompt, conversation_history)

        # Tokenize input
        inputs = self.tokenizer.encode(context, return_tensors="pt")
        if torch.cuda.is_available():
            inputs = inputs.to(self.device)

        # Generate response
        with torch.no_grad():
            outputs = self.model.generate(
                inputs,
                max_length=min(inputs.shape[1] + self.config.max_tokens, 1024),  # GPT-2 context limit
                temperature=self.config.temperature,
                top_p=self.config.top_p,
                repetition_penalty=self.config.repetition_penalty,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )

        # Decode and clean response
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = response.replace(context, "").strip()
        return self._clean_response(response)

    def _build_context(self, prompt: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """Build conversation context for GPT-2"""
        context_parts = []

        if conversation_history:
            for exchange in conversation_history[-5:]:  # Last 5 exchanges
                context_parts.append(f"User: {exchange['user']}")
                context_parts.append(f"Assistant: {exchange['assistant']}")

        context_parts.append(f"User: {prompt}")
        context_parts.append("Assistant:")

        return "\n".join(context_parts)

    def _clean_response(self, response: str) -> str:
        """Clean and post-process GPT-2 response"""
        # Remove common artifacts
        response = re.sub(r'\n+', ' ', response)
        response = re.sub(r'\s+', ' ', response)

        # Stop at common stop sequences
        stop_sequences = ["User:", "Assistant:", "\n\n", "Human:", "AI:"]
        for stop_seq in stop_sequences:
            if stop_seq in response:
                response = response.split(stop_seq)[0]

        # Remove repetitive sentences
        sentences = response.split('. ')
        unique_sentences = []
        for sentence in sentences:
            if sentence.strip() and sentence not in unique_sentences:
                unique_sentences.append(sentence)

        return '. '.join(unique_sentences).strip()

    def cleanup(self) -> None:
        """Clean up model resources"""
        if hasattr(self, 'model'):
            del self.model
        if hasattr(self, 'tokenizer'):
            del self.tokenizer
        if torch.cuda.is_available():
            torch.cuda.empty_cache()