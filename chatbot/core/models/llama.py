import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from .base import BaseChatModel
from typing import List, Dict, Optional


class LlamaChatModel(BaseChatModel):
    """Llama 3.2 1B model implementation"""

    def load_model(self) -> None:
        """Load Llama model with optimizations"""
        print(f"Loading {self.config.name} model...")

        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.config.model_path)

        # Configure quantization if enabled
        quantization_config = None
        if self.config.use_quantization and torch.cuda.is_available():
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_compute_dtype=torch.bfloat16,
                bnb_4bit_use_double_quant=True
            )

        # Load model
        self.model = AutoModelForCausalLM.from_pretrained(
            self.config.model_path,
            quantization_config=quantization_config,
            device_map=self.config.device_map if torch.cuda.is_available() else None,
            torch_dtype=getattr(torch, self.config.torch_dtype) if torch.cuda.is_available() else torch.float32,
            low_cpu_mem_usage=True
        )

        self.model.eval()
        self.device = next(self.model.parameters()).device
        print(f"Model loaded on {self.device}")

    def generate_response(self, prompt: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """Generate response using Llama"""
        # Build messages for chat template
        messages = self._build_messages(prompt, conversation_history)

        # Apply chat template
        formatted_prompt = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )

        # Tokenize
        inputs = self.tokenizer(formatted_prompt, return_tensors="pt")
        if torch.cuda.is_available():
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

        # Generate response
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                top_p=self.config.top_p,
                repetition_penalty=self.config.repetition_penalty,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )

        # Decode response
        response = self.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:],
            skip_special_tokens=True
        )

        return self._clean_response(response)

    def _build_messages(self, prompt: str, conversation_history: List[Dict[str, str]] = None) -> List[Dict[str, str]]:
        """Build message format for Llama chat template"""
        messages = [{"role": "system", "content": "You are a helpful assistant."}]

        if conversation_history:
            for exchange in conversation_history[-8:]:  # Last 8 exchanges
                messages.append({"role": "user", "content": exchange['user']})
                messages.append({"role": "assistant", "content": exchange['assistant']})

        messages.append({"role": "user", "content": prompt})
        return messages

    def _clean_response(self, response: str) -> str:
        """Clean and post-process Llama response"""
        # Remove special tokens that might leak through
        response = response.strip()

        # Handle any remaining special tokens
        special_tokens = ["<|start_header_id|>", "<|end_header_id|>", "<|eot_id|>"]
        for token in special_tokens:
            if token in response:
                response = response.split(token)[0]

        return response.strip()

    def cleanup(self) -> None:
        """Clean up model resources"""
        if hasattr(self, 'model'):
            del self.model
        if hasattr(self, 'tokenizer'):
            del self.tokenizer
        if torch.cuda.is_available():
            torch.cuda.empty_cache()