# Chatbot Configuration
# This file defines the available models and their settings

current_model: "gpt2-large"  # Default model to use

# Session settings
session_timeout: 3600  # Session timeout in seconds
max_history: 10        # Maximum conversation history to keep
save_sessions: true    # Whether to save sessions automatically
session_dir: "sessions"  # Directory to store session files

# Model configurations
models:
  gpt2-large:
    name: "GPT-2 Large"
    model_path: "openai-community/gpt2-large"
    max_tokens: 512
    temperature: 0.8
    top_p: 0.9
    repetition_penalty: 1.2
    use_quantization: true
    device_map: "auto"
    torch_dtype: "bfloat16"

  llama-3.2-1b:
    name: "Llama 3.2 1B Instruct"
    model_path: "meta-llama/Llama-3.2-1B-Instruct"
    max_tokens: 512
    temperature: 0.7
    top_p: 0.9
    repetition_penalty: 1.1
    use_quantization: true
    device_map: "auto"
    torch_dtype: "bfloat16"