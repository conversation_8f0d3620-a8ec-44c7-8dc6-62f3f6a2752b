import os
import yaml
from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from pathlib import Path


@dataclass
class ModelConfig:
    name: str
    model_path: str
    max_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    repetition_penalty: float = 1.1
    use_quantization: bool = True
    device_map: str = "auto"
    torch_dtype: str = "bfloat16"


@dataclass
class ChatConfig:
    current_model: str = "gpt2-large"
    models: Dict[str, ModelConfig] = field(default_factory=dict)
    session_timeout: int = 3600
    max_history: int = 10
    save_sessions: bool = True
    session_dir: str = "sessions"

    @classmethod
    def load_config(cls, config_path: Optional[str] = None) -> 'ChatConfig':
        """Load configuration from file with environment variable overrides"""
        if config_path is None:
            config_path = os.getenv("CHATBOT_CONFIG", "config/models.yaml")

        config_data = {}
        if Path(config_path).exists():
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)

        # Environment variable overrides
        current_model = os.getenv("CHATBOT_MODEL", config_data.get("current_model", "gpt2-large"))

        # Load model configurations
        models = {}
        for model_name, model_data in config_data.get("models", {}).items():
            models[model_name] = ModelConfig(**model_data)

        # Add default models if not present
        if "gpt2-large" not in models:
            models["gpt2-large"] = ModelConfig(
                name="GPT-2 Large",
                model_path="openai-community/gpt2-large",
                max_tokens=512,
                temperature=0.8,
                repetition_penalty=1.2
            )

        if "llama-3.2-1b" not in models:
            models["llama-3.2-1b"] = ModelConfig(
                name="Llama 3.2 1B",
                model_path="meta-llama/Llama-3.2-1B-Instruct",
                max_tokens=512,
                temperature=0.7,
                use_quantization=True
            )

        return cls(
            current_model=current_model,
            models=models,
            session_timeout=config_data.get("session_timeout", 3600),
            max_history=config_data.get("max_history", 10),
            save_sessions=config_data.get("save_sessions", True),
            session_dir=config_data.get("session_dir", "sessions")
        )