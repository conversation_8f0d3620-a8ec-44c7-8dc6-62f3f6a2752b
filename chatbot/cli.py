import typer
import sys
from pathlib import Path
from typing import Optional
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm

from .config.config import ChatConfig
from .core.chatbot import ChatBot
from .utils.errors import ChatbotError, ModelLoadError

app = typer.Typer(help="GPU-accelerated CLI chatbot with model switching")
console = Console()


class ChatInterface:
    """Interactive chat interface"""

    def __init__(self, chatbot: ChatBot):
        self.chatbot = chatbot
        self.console = Console()

    def display_welcome(self):
        """Display welcome message"""
        welcome_text = """
        🤖 GPU-Accelerated CLI Chatbot

        Commands:
        • Just type your message and press Enter
        • /help - Show this help
        • /models - List available models
        • /switch <model> - Switch to different model
        • /info - Show model information
        • /history - Show conversation history
        • /save [filename] - Save session
        • /load <filename> - Load session
        • /clear - Clear conversation
        • /exit - Exit the chat
        """
        self.console.print(Panel(welcome_text, title="Welcome", border_style="blue"))

    def display_model_info(self):
        """Display current model information"""
        info = self.chatbot.get_model_info()

        table = Table(title="Model Information")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")

        for key, value in info.items():
            if key == "memory_usage" and isinstance(value, dict):
                for mem_key, mem_value in value.items():
                    table.add_row(f"GPU {mem_key}",
                                  f"{mem_value:.2f} GB" if isinstance(mem_value, (int, float)) else str(mem_value))
            else:
                table.add_row(key.replace("_", " ").title(), str(value))

        self.console.print(table)

    def display_available_models(self):
        """Display available models"""
        models = self.chatbot.get_available_models()
        current = self.chatbot.current_model_type

        table = Table(title="Available Models")
        table.add_column("Model", style="cyan")
        table.add_column("Status", style="green")

        for model in models:
            status = "✓ Active" if model == current else "Available"
            table.add_row(model, status)

        self.console.print(table)

    def run_chat(self):
        """Run interactive chat loop"""
        self.display_welcome()

        while True:
            try:
                user_input = Prompt.ask("[bold cyan]You[/bold cyan]").strip()

                if not user_input:
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    if self.handle_command(user_input):
                        continue
                    else:
                        break

                # Generate response
                self.console.print("[dim]Thinking...[/dim]", end="")
                response = self.chatbot.chat(user_input)
                self.console.print(f"\r[bold green]Assistant[/bold green]: {response}")

            except KeyboardInterrupt:
                if Confirm.ask("\n🤔 Exit the chat?"):
                    break
            except Exception as e:
                self.console.print(f"[bold red]Error[/bold red]: {str(e)}")

    def handle_command(self, command: str) -> bool:
        """Handle chat commands. Returns True to continue, False to exit"""
        parts = command.split()
        cmd = parts[0].lower()

        try:
            if cmd == '/help':
                self.display_welcome()

            elif cmd == '/models':
                self.display_available_models()

            elif cmd == '/switch':
                if len(parts) < 2:
                    self.console.print("[red]Usage: /switch <model_name>[/red]")
                    return True

                model_name = parts[1]
                self.console.print(f"[yellow]Switching to {model_name}...[/yellow]")
                self.chatbot.switch_model(model_name)
                self.console.print(f"[green]✓ Switched to {model_name}[/green]")

            elif cmd == '/info':
                self.display_model_info()

            elif cmd == '/clear':
                self.chatbot.clear_session()
                self.console.print("[green]✓ Conversation cleared[/green]")

            elif cmd == '/save':
                filename = parts[1] if len(parts) > 1 else None
                saved_file = self.chatbot.save_session(filename)
                self.console.print(f"[green]✓ Session saved to {saved_file}[/green]")

            elif cmd == '/load':
                if len(parts) < 2:
                    self.console.print("[red]Usage: /load <filename>[/red]")
                    return True

                filename = parts[1]
                self.chatbot.load_session(filename)
                self.console.print(f"[green]✓ Session loaded from {filename}[/green]")

            elif cmd == '/exit':
                return False

            else:
                self.console.print(f"[red]Unknown command: {cmd}[/red]")

        except Exception as e:
            self.console.print(f"[red]Command error: {str(e)}[/red]")

        return True


@app.command()
def chat(
        model: str = typer.Option("gpt2-large", "--model", "-m", help="Model to use"),
        config: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file"),
        verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output")
):
    """Start interactive chat session"""
    try:
        # Load configuration
        chat_config = ChatConfig.load_config(config)

        # Override model if specified
        if model in chat_config.models:
            chat_config.current_model = model

        # Initialize chatbot
        chatbot = ChatBot(chat_config)
        console.print("[yellow]Initializing chatbot...[/yellow]")
        chatbot.initialize()

        # Start chat interface
        interface = ChatInterface(chatbot)
        interface.run_chat()

    except ModelLoadError as e:
        console.print(f"[bold red]Model loading error:[/bold red] {str(e)}")
        raise typer.Exit(1)
    except ChatbotError as e:
        console.print(f"[bold red]Chatbot error:[/bold red] {str(e)}")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[bold red]Unexpected error:[/bold red] {str(e)}")
        if verbose:
            import traceback
            traceback.print_exc()
        raise typer.Exit(1)
    finally:
        # Cleanup
        if 'chatbot' in locals():
            chatbot.cleanup()


if __name__ == "__main__":
    app()