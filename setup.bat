@echo off
REM Windows batch script to setup the Simple Chatbot environment

echo 🚀 Setting up Simple Chatbot Environment
echo ================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
python --version

REM Run the setup script
echo.
echo 🔧 Running setup script...
python setup_env.py

if errorlevel 1 (
    echo ❌ Setup failed
    pause
    exit /b 1
)

echo.
echo ✅ Setup completed successfully!
echo.
echo To start using the chatbot:
echo 1. Run: venv\Scripts\activate
echo 2. Run: python -m chatbot.cli chat
echo.
pause
