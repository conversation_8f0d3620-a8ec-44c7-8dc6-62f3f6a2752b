"""Setup script for the Simple Chatbot package"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="simple-chatbot",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A GPU-accelerated CLI chatbot with model switching capabilities",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/simple_chatbot",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "chatbot=chatbot.cli:app",
        ],
    },
    include_package_data=True,
    package_data={
        "chatbot": ["config/*.yaml"],
    },
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "black>=23.11.0",
            "mypy>=1.7.1",
        ],
    },
)