#!/bin/bash
# Linux/Mac shell script to setup the Simple Chatbot environment

echo "🚀 Setting up Simple Chatbot Environment"
echo "================================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8 or higher"
    exit 1
fi

echo "✅ Python found"
python3 --version

# Run the setup script
echo ""
echo "🔧 Running setup script..."
python3 setup_env.py

if [ $? -ne 0 ]; then
    echo "❌ Setup failed"
    exit 1
fi

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "To start using the chatbot:"
echo "1. Run: source venv/bin/activate"
echo "2. Run: python -m chatbot.cli chat"
echo ""
