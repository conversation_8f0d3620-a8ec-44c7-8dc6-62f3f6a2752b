#!/usr/bin/env python3
"""
Setup script for creating and configuring the virtual environment for the Simple Chatbot project.
This script automates the process of setting up the Python virtual environment with CUDA 11.8 support.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, check=True, shell=False):
    """Run a command and return the result"""
    print(f"Running: {command}")
    try:
        if isinstance(command, str) and not shell:
            command = command.split()
        result = subprocess.run(command, check=check, capture_output=True, text=True, shell=shell)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major != 3 or version.minor < 8:
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        sys.exit(1)
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")


def check_cuda():
    """Check CUDA availability"""
    try:
        result = run_command("nvidia-smi", check=False)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected")
            return True
        else:
            print("⚠️  NVIDIA GPU not detected or nvidia-smi not available")
            return False
    except FileNotFoundError:
        print("⚠️  nvidia-smi not found. CUDA may not be available")
        return False


def create_virtual_environment():
    """Create a virtual environment"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("📁 Virtual environment already exists")
        return venv_path
    
    print("🔧 Creating virtual environment...")
    run_command([sys.executable, "-m", "venv", "venv"])
    print("✅ Virtual environment created")
    return venv_path


def get_activation_command():
    """Get the command to activate the virtual environment"""
    if platform.system() == "Windows":
        return "venv\\Scripts\\activate"
    else:
        return "source venv/bin/activate"


def get_pip_command():
    """Get the pip command for the virtual environment"""
    if platform.system() == "Windows":
        return "venv\\Scripts\\pip"
    else:
        return "venv/bin/pip"


def install_requirements():
    """Install requirements in the virtual environment"""
    pip_cmd = get_pip_command()
    
    print("📦 Upgrading pip...")
    run_command([pip_cmd, "install", "--upgrade", "pip"])
    
    print("📦 Installing requirements...")
    run_command([pip_cmd, "install", "-r", "requirements.txt"])
    
    print("📦 Installing package in development mode...")
    run_command([pip_cmd, "install", "-e", "."])


def create_directories():
    """Create necessary directories"""
    directories = ["sessions", "logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")


def test_installation():
    """Test the installation"""
    print("🧪 Testing installation...")
    
    if platform.system() == "Windows":
        python_cmd = "venv\\Scripts\\python"
    else:
        python_cmd = "venv/bin/python"
    
    # Test imports
    test_script = """
import torch
import transformers
from chatbot.config.config import ChatConfig
from chatbot.core.chatbot import ChatBot

print("✅ All imports successful")
print(f"PyTorch version: {torch.__version__}")
print(f"Transformers version: {transformers.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA version: {torch.version.cuda}")
    print(f"GPU count: {torch.cuda.device_count()}")
"""
    
    with open("test_imports.py", "w") as f:
        f.write(test_script)
    
    try:
        run_command([python_cmd, "test_imports.py"])
        print("✅ Installation test passed")
    finally:
        # Clean up test file
        Path("test_imports.py").unlink(missing_ok=True)


def print_usage_instructions():
    """Print usage instructions"""
    activation_cmd = get_activation_command()
    
    print("\n" + "="*60)
    print("🎉 Setup completed successfully!")
    print("="*60)
    print("\n📋 Usage Instructions:")
    print(f"1. Activate the virtual environment:")
    print(f"   {activation_cmd}")
    print("\n2. Run the chatbot:")
    print("   python -m chatbot.cli chat")
    print("   # or")
    print("   chatbot chat")
    print("\n3. Available options:")
    print("   chatbot chat --model gpt2-large")
    print("   chatbot chat --model llama-3.2-1b")
    print("   chatbot chat --config custom_config.yaml")
    print("   chatbot chat --verbose")
    print("\n4. In the chat interface, use these commands:")
    print("   /help     - Show help")
    print("   /models   - List available models")
    print("   /switch <model> - Switch models")
    print("   /info     - Show model info")
    print("   /clear    - Clear conversation")
    print("   /save     - Save session")
    print("   /exit     - Exit chat")
    print("\n📝 Note: The first time you run a model, it will be downloaded")
    print("   from Hugging Face, which may take some time.")
    print("\n🔧 Configuration file: chatbot/config/models.yaml")
    print("📁 Sessions saved to: sessions/")


def main():
    """Main setup function"""
    print("🚀 Setting up Simple Chatbot Environment")
    print("="*50)
    
    # Check prerequisites
    check_python_version()
    has_cuda = check_cuda()
    
    if not has_cuda:
        print("⚠️  Continuing without CUDA. Models will run on CPU.")
        input("Press Enter to continue or Ctrl+C to abort...")
    
    # Setup environment
    create_virtual_environment()
    create_directories()
    install_requirements()
    test_installation()
    
    # Print instructions
    print_usage_instructions()


if __name__ == "__main__":
    main()
